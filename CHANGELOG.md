# 更新日志 (CHANGELOG)

本文档记录项目的所有重要变更内容。

## [未发布] - 2025-08-07

### 新增 ✨
- 在项目顶部栏公司选择框添加复制按钮功能 (2025-08-07)
- 支持勾选部分数据生成凭证功能 (2025-08-05)
- AI聊天页面新增语音录入功能 (2025-08-01)
- 新增第三方平台账号配置功能和数据源切换功能

### 修改 🔧
- 优化科目映射功能，实现科目选择逻辑优化和分页修复 (2025-08-07)
- 科目映射配置功能完善 (2025-08-06)
- 移除科目映射配置页面中的会计科目配置部分，简化页面结构
- 为数据源切换弹框添加数据清空警告提示
- 新增原始凭证货物维度展示功能
- 支持仅附件发送消息功能
- 实现第三方平台认证和自动登录功能
- 添加凭证数量单价显示功能
- 新增财务报表模块，包含资产、负债、所有者权益、成本、损益五个tab页的综合报表展示
- 优化科目映射配置页面，将原单一表格改为按科目类别分tab显示（资产、负债、所有者权益、成本、损益），提升用户体验
- 增强科目映射功能，实际适用记账科目列可点击，支持通过弹窗选择树状结构的实际科目进行映射

### 改进 🚀
- 凭证编辑页面会计科目切换后保存时正确拆解科目和辅助核算信息 (2025-08-04)
- 为发票场景更新接口增加detail_id参数支持 (2025-08-04)
- 优化科目选择器，移除场景分录配置中的辅助核算信息显示 (2025-07-31)
- 更新文件上传消息中的location_type字段
- 更换AI聊天标题栏图标
- 优化消息类型处理，支持动态处理所有新消息类型
- 重构文件上传功能并更新生产环境配置
- 完善API函数返回类型声明，修复类型提示缺失问题
- 优化凭证编辑和原始发票处理功能
- 优化原始凭证页面筛选和显示功能

### 修复 🐛
- 修复场景分录编辑弹框中分录详情选择框数据源选项问题 (2025-08-01)
- 发送图片url地址修改 (2025-08-01)
- 修复发票货物维度展示和状态同步问题
- 修复页面初始化时开票日期设置时序问题和日期字段清空逻辑
- 修复发票页面日期字段清空后仍使用默认月份查询的问题
- 修复全选功能仅作用于当前筛选结果

### 移除 ❌
- 删除MIT许可证 (2025-08-06)
- 注释未使用的SSE关闭接口 (2025-08-05)
- 移除银行回单接口请求中的month参数
- 移除客户状态页面的AI自动功能
- 移除场景入口配置的删除功能

## [2025-07-30] - 版本归档

### 系统优化 🚀
- 接口鉴权功能增强
- 凭证列表添加筛选功能和Tooltip提示
- 银行名称筛选增加账号显示
- 批量写入添加二次确认弹窗
- 限制页面最小宽度，优化布局体验
- 税款所属期筛选功能
- 缓存滚动位置，保留页面返回时的滚动位置
- 同步客户列表功能
- 更多筛选功能
- 凭证列表增加筛选功能
- 展开AI对话框后,自动打开"原始票据"的页面

### 数据处理 📊
- 发票和回单原文件改为oss存储
- 增加清除当月公司数据功能
- 科目显示使用fullName替代name字段
- 动态获取请求地址配置
- 增加config配置文件
- 为companies/sync接口添加月份参数支持
- 简化 users/get_user_info 接口返回参数，移除 yqdz 字段
- API结构重构

### 界面优化 🎨
- 菜单重构和名称修改
- 客户列表样式优化
- 顶部栏样式调整
- 凭证表格对齐优化
- 样式调整和文案修改

### 功能修复 🐛
- 修复切换公司后，凭证页不显示问题
- 修复回单修改场景
- 修复税率处理逻辑
- 凭证列表重复刷新bug修复
- 合计大写问题修复
- 写入状态异常修复
- AI助手发送按钮重复发送消息问题修复
- 修复热重载重复注册方法问题
- 读取config配置bug修复

### 性能优化 ⚡
- 凭证列表使用虚拟列表实现，优化大数据量下渲染性能
- 整理项目结构
- hooks整理
- 编辑凭证页面代码整理

### 业务逻辑 💼
- 修改AI助手银行回单和发票处理完成后跳转逻辑
- 切换场景时需要选择对应原始凭证类型的记账场景
- 增加税款所属期

---

**说明**: 
- 🆕 表示新功能
- 🚀 表示功能改进
- 🐛 表示问题修复
- ❌ 表示移除功能
- 📝 表示文档更新

项目基于 Vue 3 + Ant Design Vue + TypeScript 技术栈构建。

